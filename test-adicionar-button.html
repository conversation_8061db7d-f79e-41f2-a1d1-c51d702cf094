<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test - Adicionar <PERSON> & Real-time Updates</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }
    
    .ativos_main-list {
      min-height: 200px;
      border: 2px solid #ccc;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }
    
    .ativos_main_drop_area {
      min-height: 200px;
      border: 2px dashed #007bff;
      padding: 15px;
      background-color: #e7f3ff;
      border-radius: 8px;
    }
    
    .w-dyn-item {
      padding: 10px 15px;
      margin: 5px 0;
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: move;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .w-dyn-item:hover {
      background-color: #f0f0f0;
    }
    
    .counter_ativos {
      font-weight: bold;
      color: #007bff;
      font-size: 18px;
    }
    
    .add_ativo_manual {
      margin: 15px 0;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #dee2e6;
    }
    
    .add_ativo_manual.desativado {
      display: none;
    }
    
    #adicionarNAtivo {
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
      width: 200px;
    }
    
    #adicionarAtivo {
      padding: 8px 16px;
      background-color: #28a745;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 10px;
    }
    
    #adicionarAtivo:hover {
      background-color: #218838;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
    }
    
    .instructions {
      background-color: #e9ecef;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    
    .instructions h3 {
      margin-top: 0;
      color: #495057;
    }
    
    .instructions ul {
      margin-bottom: 0;
    }
    
    .test-status {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h1>Test: Adicionar Button & Real-time List Updates</h1>
  
  <div class="instructions">
    <h3>Test Instructions:</h3>
    <ul>
      <li><strong>Issue 1 Fix:</strong> New items should now appear immediately in the source list (left side) without page refresh</li>
      <li><strong>Issue 2 Fix:</strong> Click "Mostrar campo" to reveal input, then use either Enter key OR the new "Adicionar" button</li>
      <li><strong>Drag Test:</strong> After adding items, drag them from source list to drop area (right side)</li>
      <li><strong>Counter Test:</strong> Counter should update when items are moved to drop area</li>
    </ul>
  </div>
  
  <div class="counter_ativos">Items in Drop Area: (0)</div>
  
  <div class="container">
    <div>
      <div class="section-title">📋 Source List (Draggable Items)</div>
      <div class="ativos_main-list">
        <!-- Template item (hidden, used for cloning) -->
        <div id="pillAtivo" style="display: none;">
          <span>Template Item</span>
        </div>
        
        <!-- Existing items -->
        <div class="w-dyn-item" data-ativo-item="true">
          <span>Existing Item 1</span>
        </div>
        <div class="w-dyn-item" data-ativo-item="true">
          <span>Existing Item 2</span>
        </div>
        
        <!-- Add new asset button -->
        <button id="adicionarAtivo">Mostrar campo para adicionar</button>
        
        <!-- Add new asset input (initially hidden) -->
        <div class="add_ativo_manual desativado">
          <input type="text" id="adicionarNAtivo" placeholder="Digite o nome do novo ativo..." />
          <!-- The "Adicionar" button will be inserted here by JavaScript -->
        </div>
      </div>
    </div>
    
    <div>
      <div class="section-title">🎯 Drop Area (Final Destination)</div>
      <div class="ativos_main_drop_area">
        <p style="color: #666; font-style: italic;">Drag items here from the source list</p>
      </div>
    </div>
  </div>
  
  <div class="test-status">
    <strong>Expected Behavior:</strong>
    <br>1. Click "Mostrar campo" → Input field appears
    <br>2. Type item name → Press Enter OR click "Adicionar" button
    <br>3. New item appears immediately in source list (left side)
    <br>4. Input field hides automatically
    <br>5. You can drag the new item to the drop area (right side)
    <br>6. Counter updates when items are in drop area
  </div>

  <!-- Load the built JavaScript -->
  <script src="./dist/index.js"></script>
  
  <script>
    // Additional test logging
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Test page loaded');
      
      // Log when items are added
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
              if (node.nodeType === 1 && node.classList && node.classList.contains('w-dyn-item')) {
                console.log('✅ New item added to DOM:', node.textContent);
              }
            });
          }
        });
      });
      
      observer.observe(document.querySelector('.ativos_main-list'), {
        childList: true,
        subtree: true
      });
      
      // Log counter updates
      document.addEventListener('counterUpdate', function(e) {
        console.log('📊 Counter updated:', e.detail.count);
      });
    });
  </script>
</body>
</html>
